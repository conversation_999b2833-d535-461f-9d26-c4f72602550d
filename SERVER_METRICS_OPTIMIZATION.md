# 服务器指标功能优化说明

## 📋 优化概述

本次优化移除了 `model_storage_server_metrics` 表的数据库存储功能，改为实时获取服务器指标数据，以提高系统性能和减少存储压力。

## 🎯 优化目标

1. **减少数据库存储压力** - 移除最大的表（11,593行，1.52MB）
2. **提高响应速度** - 避免数据库写入操作的延迟
3. **解决SQLite锁定问题** - 消除并发写入导致的数据库锁定
4. **简化系统架构** - 减少不必要的数据持久化

## 🔧 技术变更

### 代码变更

#### 1. 视图层优化 (`spug_api/apps/model_storage/views.py`)

**变更前:**
```python
# 保存到数据库（使用重试机制处理SQLite锁定）
def save_metrics_with_retry():
    # ... 复杂的重试逻辑
    ServerMetrics.objects.create(**metrics)
    # ... 清理过期数据逻辑
```

**变更后:**
```python
# 优化：不再保存到数据库，直接返回实时数据
# 这样可以减少数据库存储压力，提高响应速度，避免SQLite锁定问题
logger.debug(f"获取远程服务器 {target_host} 指标成功，返回实时数据")
```

#### 2. 模型层变更 (`spug_api/apps/model_storage/models.py`)

**移除的模型:**
```python
class ServerMetrics(models.Model, ModelMixin):
    """服务器指标模型"""
    disk_usage = models.FloatField('磁盘使用率')
    cpu_usage = models.FloatField('CPU使用率')
    memory_usage = models.FloatField('内存使用率', default=0.0)
    network_upload = models.CharField('网络上传', max_length=32, default='0MB')
    network_download = models.CharField('网络下载', max_length=32, default='0MB')
    network_total = models.CharField('网络总计', max_length=32, default='0MB')
    timestamp = models.DateTimeField(auto_now_add=True)
```

#### 3. 数据库迁移

创建了迁移文件 `0014_remove_servermetrics.py` 来删除表结构。

### 前端保持不变

前端代码无需修改，因为API接口保持相同的响应格式：

```javascript
// spug_web/src/pages/model-storage/store.js
@action
fetchMetrics = async () => {
  const res = await http.get('/api/model-storage/server-metrics/');
  // 响应格式保持不变
  this.metrics = {
    cpu: res.cpu_usage || 0,
    memory: res.memory_usage || 0,
    disk: res.disk_usage || 0,
    // ...
  };
};
```

## 📊 性能提升

### 数据库优化
- **存储空间释放**: ~1.52MB
- **记录数减少**: 11,593条记录
- **写入操作消除**: 每5秒一次的数据库写入操作

### 响应速度提升
- **消除数据库写入延迟**: 不再需要等待数据库写入完成
- **避免SQLite锁定**: 消除并发访问导致的锁定问题
- **减少重试逻辑**: 移除复杂的数据库重试机制

### 系统稳定性
- **减少数据库压力**: 特别是在高频访问时
- **简化错误处理**: 不再需要处理数据库写入失败
- **提高并发性能**: 避免数据库锁定影响其他操作

## 🚀 部署步骤

### 1. 备份数据库
```bash
# 在29服务器上执行
/root/spug_backup.sh
```

### 2. 应用代码变更
```bash
# 上传修改后的代码文件
# - spug_api/apps/model_storage/views.py
# - spug_api/apps/model_storage/models.py
# - spug_api/apps/model_storage/migrations/0014_remove_servermetrics.py
```

### 3. 执行优化脚本
```bash
python optimize_server_metrics.py
```

### 4. 重启服务
```bash
# 重启Django应用
systemctl restart spug-api
# 或者
supervisorctl restart spug-api
```

## ✅ 验证步骤

### 1. 检查表是否删除
```sql
-- 在数据库中执行
SELECT name FROM sqlite_master 
WHERE type='table' AND name='model_storage_server_metrics';
-- 应该返回空结果
```

### 2. 测试API功能
```bash
curl -X GET "http://localhost:8000/api/model-storage/server-metrics/?host=**********"
```

### 3. 检查前端显示
访问模型存储页面，确认服务器指标正常显示。

## 🔄 回滚方案

如果需要回滚，可以：

1. **恢复代码**: 使用git回滚到优化前的版本
2. **恢复数据库**: 使用备份恢复数据库
3. **重新迁移**: 重新应用包含ServerMetrics的迁移

## 📈 监控建议

优化后建议监控以下指标：

1. **API响应时间**: 服务器指标接口的响应速度
2. **数据库大小**: 确认数据库大小减少
3. **系统稳定性**: 观察是否还有SQLite锁定问题
4. **用户体验**: 前端页面加载和刷新速度

## 🎉 预期效果

- ✅ 数据库存储减少 ~1.5MB
- ✅ API响应速度提升 20-50%
- ✅ 消除SQLite锁定问题
- ✅ 系统架构更简洁
- ✅ 维护成本降低
