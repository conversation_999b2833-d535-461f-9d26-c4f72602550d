# Docker容器文件分发功能

## 功能概述

本次更新为Spug文件分发功能添加了Docker容器支持，允许用户将文件直接分发到Docker容器内，同时修复了重复输入的问题，并添加了配置复用功能。

## 新增功能

### 1. Docker容器分发模式

- **容器选择**: 自动获取主机上运行中的Docker容器列表
- **容器内分发**: 支持将文件分发到指定容器的目标路径
- **路径自动补全**: 在Docker模式下支持容器内路径的智能补全
- **状态验证**: 自动验证容器是否存在且正在运行

### 2. 路径模糊匹配

- **智能补全**: 输入路径时自动提供匹配建议
- **文件类型识别**: 区分文件和目录，提供相应图标
- **实时搜索**: 支持实时路径搜索和匹配

### 3. 配置复用功能

- **保存模板**: 将常用的分发配置保存为模板
- **快速应用**: 一键应用已保存的配置模板
- **公开共享**: 支持将模板设为公开，供其他用户使用
- **模板管理**: 支持编辑、删除自己创建的模板

### 4. 用户体验优化

- **修复重复输入**: 优化主机文件选择流程，避免重复输入路径
- **界面改进**: 更直观的Docker模式切换和容器选择界面
- **状态反馈**: 更详细的执行状态和错误提示

## 使用方法

### 启用Docker模式

1. 在文件分发页面的"分发目标"区域
2. 切换"分发模式"开关到Docker容器模式
3. 系统会自动为选择的主机获取容器列表

### 选择目标容器

1. 选择目标主机后，系统自动获取该主机的Docker容器
2. 为每个主机选择对应的目标容器
3. 支持搜索和过滤容器列表

### 路径自动补全

1. 在Docker模式下，目标路径输入框支持自动补全
2. 输入路径时会显示匹配的文件和目录
3. 点击建议项可快速选择路径

### 使用配置模板

1. 点击"使用模板"按钮查看可用模板
2. 选择合适的模板并点击"应用"
3. 系统会自动填充相关配置

### 保存配置模板

1. 配置好分发参数后，点击"保存模板"
2. 输入模板名称和描述
3. 选择是否设为公开模板
4. 保存后可在模板列表中管理

## 技术实现

### 后端API

- `GET /api/exec/docker-containers/`: 获取Docker容器列表
- `GET /api/exec/docker-path-suggestions/`: 获取路径建议
- `GET /api/exec/transfer-templates/`: 获取配置模板
- `POST /api/exec/transfer-templates/`: 创建配置模板
- `PATCH /api/exec/transfer-templates/{id}/`: 更新配置模板
- `DELETE /api/exec/transfer-templates/{id}/`: 删除配置模板

### 数据库变更

- `Transfer`模型添加`extra`字段存储Docker配置
- 新增`TransferTemplate`模型存储配置模板

### 前端组件

- 增强的文件分发界面，支持Docker模式切换
- 容器选择器组件
- 路径自动补全组件
- 配置模板管理界面

## 部署说明

### 数据库迁移

```bash
python manage.py migrate exec
```

### 权限要求

- 用户需要有`exec.transfer.do`权限才能使用文件分发功能
- Docker容器操作需要主机上安装Docker并且Spug用户有相应权限

### 环境要求

- 目标主机需要安装Docker
- 目标主机的SSH用户需要有Docker操作权限
- 建议使用Docker组权限而非root权限

## 注意事项

1. **容器状态**: 只能向运行中的容器分发文件
2. **路径权限**: 确保容器内目标路径有写入权限
3. **文件大小**: 大文件分发可能需要较长时间
4. **网络连接**: 确保Spug服务器能够SSH连接到目标主机
5. **Docker版本**: 建议使用Docker 18.03+版本

## 故障排除

### 容器列表为空
- 检查主机上是否有运行中的容器
- 验证SSH用户是否有Docker权限
- 检查Docker服务是否正常运行

### 路径补全不工作
- 确认容器正在运行
- 检查容器内路径是否存在
- 验证容器权限设置

### 分发失败
- 检查目标路径权限
- 确认容器存储空间充足
- 查看详细错误日志

## 更新日志

- 添加Docker容器分发支持
- 实现路径模糊匹配功能
- 修复重复输入bug
- 添加配置复用功能
- 优化用户界面和体验
