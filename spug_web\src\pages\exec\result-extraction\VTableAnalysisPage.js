import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, message, Space, Spin, Progress, Tag, Breadcrumb, Modal, Input } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, ReloadOutlined, Bar<PERSON><PERSON>Outlined, SaveOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import * as VTable from '@visactor/vtable';
import * as XLSX from 'xlsx';
import { http } from 'libs';

// 智能提取模板 - 基于您提供的固定格式，支持多个空格
const LOG_EXTRACTION_TEMPLATE = {
  patterns: [
    { key: 'successful_requests', pattern: /Successful requests:\s+([\d,]+)/, type: 'number' },
    { key: 'benchmark_duration', pattern: /Benchmark duration \(s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_input_tokens', pattern: /Total input tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'total_generated_tokens', pattern: /Total generated tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'request_throughput', pattern: /Request throughput \(req\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'output_token_throughput', pattern: /Output token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_token_throughput', pattern: /Total Token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_ttft', pattern: /Mean TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_ttft', pattern: /Median TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_ttft', pattern: /P99 TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_tpot', pattern: /Mean TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_tpot', pattern: /Median TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_tpot', pattern: /P99 TPOT \(ms\):\s+([\d.]+)/, type: 'number' }
  ]
};

// VTable列配置
const VTABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'left', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['filename'],
      filterMode: 'fuzzyMatch'
    }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['successful_requests'],
      filterMode: 'condition'
    }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff7e6' },
    style: { textAlign: 'center', color: '#fa8c16' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['benchmark_duration'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 140,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_input_tokens'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 140,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_generated_tokens'],
      filterMode: 'condition'
    }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['request_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['output_token_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center', color: '#52c41a' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['total_token_throughput'],
      filterMode: 'condition'
    }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['mean_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['median_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2f0' },
    style: { textAlign: 'center', color: '#ff4d4f' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['p99_ttft'],
      filterMode: 'condition'
    }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['mean_tpot'],
      filterMode: 'condition'
    }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['median_tpot'],
      filterMode: 'condition'
    }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' },
    sort: true, // 启用排序
    filter: { // 启用筛选
      filterKeys: ['p99_tpot'],
      filterMode: 'condition'
    }
  }
];

const VTableAnalysisPage = () => {
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [progress, setProgress] = useState(0);
  const [tableInstance, setTableInstance] = useState(null);
  const [analysisParams, setAnalysisParams] = useState(null);
  const [saveModalVisible, setSaveModalVisible] = useState(false);
  const [saveName, setSaveName] = useState('');
  const [saveDescription, setSaveDescription] = useState('');
  const [saving, setSaving] = useState(false);
  const [sortState, setSortState] = useState([]); // 排序状态
  const containerRef = useRef(null);
  const history = useHistory();

  // 加载测试结果数据
  const loadTestResultData = async (resultId) => {
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-results/${resultId}/?token=1`);

      if (response) {
        // 如果有原始日志，优先使用原始日志数据
        if (response.raw_log) {
          try {
            const parsedLog = JSON.parse(response.raw_log);
            if (Array.isArray(parsedLog) && parsedLog.length > 0) {
              setExtractedData(parsedLog);
              message.success('测试结果数据加载成功');
              return;
            }
          } catch (e) {
            console.log('原始日志不是JSON格式，尝试解析指标数据');
          }
        }

        // 如果没有原始日志或解析失败，使用指标数据
        if (response.metrics && response.metrics.length > 0) {
          // 创建一个基础的VTable行数据
          const vtableRow = {
            filename: response.plan_name || `测试结果_${resultId}`,
            successful_requests: '',
            benchmark_duration: '',
            total_input_tokens: '',
            total_generated_tokens: '',
            request_throughput: '',
            output_token_throughput: '',
            total_token_throughput: '',
            mean_ttft: '',
            median_ttft: '',
            p99_ttft: '',
            mean_tpot: '',
            median_tpot: '',
            p99_tpot: ''
          };

          // 根据指标名称映射到对应字段
          response.metrics.forEach(metric => {
            const name = metric.name.toLowerCase();
            const value = parseFloat(metric.value) || metric.value;

            if (name.includes('successful requests')) {
              vtableRow.successful_requests = value;
            } else if (name.includes('benchmark duration')) {
              vtableRow.benchmark_duration = value;
            } else if (name.includes('total input tokens')) {
              vtableRow.total_input_tokens = value;
            } else if (name.includes('total generated tokens')) {
              vtableRow.total_generated_tokens = value;
            } else if (name.includes('request throughput')) {
              vtableRow.request_throughput = value;
            } else if (name.includes('output token throughput')) {
              vtableRow.output_token_throughput = value;
            } else if (name.includes('total token throughput')) {
              vtableRow.total_token_throughput = value;
            } else if (name.includes('mean ttft')) {
              vtableRow.mean_ttft = value;
            } else if (name.includes('median ttft')) {
              vtableRow.median_ttft = value;
            } else if (name.includes('p99 ttft')) {
              vtableRow.p99_ttft = value;
            } else if (name.includes('mean tpot')) {
              vtableRow.mean_tpot = value;
            } else if (name.includes('median tpot')) {
              vtableRow.median_tpot = value;
            } else if (name.includes('p99 tpot')) {
              vtableRow.p99_tpot = value;
            }
          });

          setExtractedData([vtableRow]);
          message.success('测试结果数据加载成功');
        } else {
          message.error('测试结果中没有找到指标数据');
        }
      } else {
        message.error('测试结果数据格式错误');
      }
    } catch (error) {
      console.error('加载测试结果失败:', error);
      message.error('加载测试结果失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 处理后端解析的数据
  const processBackendData = useCallback((parsedData, filename) => {
    // 如果后端已经解析了数据，直接使用
    if (parsedData && typeof parsedData === 'object') {
      return { filename, ...parsedData };
    }

    // 如果后端没有解析数据，返回默认结构
    const defaultResult = { filename };
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type }) => {
      defaultResult[key] = type === 'number' ? 0 : '-';
    });

    return defaultResult;
  }, []);

  // 获取文件内容并提取数据
  const processFiles = useCallback(async () => {
    if (!analysisParams || !analysisParams.selectedFiles) {
      message.error('缺少分析参数，请重新选择文件');
      return;
    }

    const { selectedFiles, hostId, containerName } = analysisParams;
    setLoading(true);
    setProgress(0);
    const results = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setProgress(Math.round(((i + 1) / selectedFiles.length) * 100));

        try {
          // 调用API获取文件内容和解析数据
          const apiUrl = `/api/exec/docker-file-content/?host_id=${hostId}&container_name=${containerName}&file_path=${encodeURIComponent(file.fullPath)}&token=1`;
          const response = await fetch(apiUrl);

          if (response.ok) {
            const responseData = await response.json();

            // 检查spug的标准响应格式: {data: {...}, error: ''}
            if (responseData.error) {
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])) });
            } else if (responseData.data) {
              const data = responseData.data;

              if (data.success) {
                const processedData = processBackendData(data.parsed_data, file.name);
                results.push(processedData);
              } else {
                results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '-'])) });
              }
            } else {
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '格式错误'])) });
            }
          } else {
            results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : 'HTTP错误'])) });
          }
        } catch (error) {
          results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '请求异常'])) });
        }

        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setExtractedData(results);

      if (results.length > 0) {
        message.success(`成功分析 ${results.length} 个日志文件`);
      } else {
        message.warning('未能提取到有效数据');
      }
    } catch (error) {
      message.error('分析过程中发生错误: ' + error.message);
    } finally {
      setLoading(false);
      setProgress(0);
    }
  }, [analysisParams, processBackendData]);

  // 导出数据到Excel
  const exportData = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      const worksheet = XLSX.utils.json_to_sheet(extractedData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '基准测试结果');

      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `基准测试分析结果_${timestamp}.xlsx`;

      // 使用浏览器原生下载
      const url = window.URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('数据导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + error.message);
    }
  };

  // 保存结果记录
  const handleSave = () => {
    if (extractedData.length === 0) {
      message.warning('没有数据可保存');
      return;
    }

    // 生成默认名称
    const timestamp = new Date().toLocaleString('zh-CN');
    const defaultName = `基准测试分析结果_${timestamp}`;
    setSaveName(defaultName);
    setSaveDescription(`包含 ${extractedData.length} 个文件的基准测试分析结果`);
    setSaveModalVisible(true);
  };

  // 确认保存
  const confirmSave = async () => {
    if (!saveName.trim()) {
      message.error('请输入结果名称');
      return;
    }

    setSaving(true);
    try {
      const saveData = {
        plan_name: saveName.trim(), // 使用name作为plan_name
        task_name: saveDescription.trim() || '',
        source_type: 'log_extraction',
        metrics: extractedData.map(item => ({
          label: `${item.文件名} - ${item.模型名称}`,
          value: item.推理吞吐量,
          unit: item.单位 || 'req/s',
          confidence: 1.0,
          category: 'benchmark',
          file_name: item.文件名,
          model_name: item.模型名称,
          batch_size: item.批次大小,
          sequence_length: item.序列长度,
          throughput: item.推理吞吐量,
          latency: item.延迟
        })),
        raw_log: JSON.stringify(extractedData, null, 2),
        total_metrics: extractedData.length,
        confirmed_metrics: extractedData.length,
        ai_confidence: 1.0,
        source: 'vtable_analysis',
        log_source_host: analysisParams?.hostId,
        log_source_path: analysisParams?.containerName,
        log_extraction_method: 'vtable_analysis'
      };

      await http.post('/api/exec/test-results/', saveData);
      message.success('结果保存成功');
      setSaveModalVisible(false);
      setSaveName('');
      setSaveDescription('');
    } catch (error) {
      console.error('保存错误详情:', error);
      const errorMsg = error.response?.data?.error || error.response?.data?.message || error.message || '未知错误';
      message.error('保存失败: ' + errorMsg);
    } finally {
      setSaving(false);
    }
  };

  // 初始化VTable
  useEffect(() => {
    // 清理之前的表格实例
    if (tableInstance) {
      try {
        tableInstance.release();
      } catch (e) {
        console.warn('清理VTable实例时出错:', e);
      }
      setTableInstance(null);
    }

    if (extractedData.length > 0) {
      // 使用setTimeout确保DOM已经渲染
      const timer = setTimeout(() => {
        if (containerRef.current && !tableInstance) {
          try {
            const option = {
              records: extractedData,
              columns: VTABLE_COLUMNS,
              widthMode: 'standard',
              heightMode: 'autoHeight',
              autoWrapText: true,
              multipleSort: true, // 启用多列排序
              sortState: sortState, // 设置初始排序状态
              theme: VTable.themes.DEFAULT.extends({
                headerStyle: {
                  bgColor: '#fafafa',
                  color: '#000',
                  fontWeight: 'bold'
                },
                bodyStyle: {
                  bgColor: '#fff',
                  color: '#000'
                }
              }),
              // 启用选择功能
              select: {
                highlightMode: 'cell',
                disableSelect: false,
                disableHeaderSelect: false
              },
              // 启用复制功能
              allowCopy: true,
              // 复制配置
              copyConfig: {
                // 复制时包含表头
                includeHeader: true,
                // 复制分隔符
                separatorColumn: '\t',
                separatorRow: '\n'
              },
              // 启用键盘导航
              keyboardOptions: {
                selectAllOnCtrlA: true,
                copyOnCtrlC: true,
                pasteOnCtrlV: false
              }
            };

            const table = new VTable.ListTable(containerRef.current, option);

            // 监听排序状态变化
            table.on('sort-state-change', (state) => {
              setSortState(state);
            });

            // 添加键盘事件监听
            const handleKeyDown = (event) => {
              if (event.ctrlKey && event.key === 'c') {
                // 阻止默认行为
                event.preventDefault();

                // 获取选中的单元格数据并复制
                const selectedRanges = table.getSelectedCellInfos();
                if (selectedRanges && selectedRanges.length > 0) {
                  // 如果有选中的单元格，复制选中内容
                  table.copy();
                  message.success('已复制选中内容到剪贴板');
                } else {
                  // 如果没有选中内容，复制整个表格
                  table.selectAll();
                  setTimeout(() => {
                    table.copy();
                    message.success('已复制整个表格到剪贴板');
                  }, 100);
                }
              }
            };

            // 为表格容器添加键盘事件监听
            const tableContainer = containerRef.current;
            if (tableContainer) {
              tableContainer.addEventListener('keydown', handleKeyDown);
              // 确保容器可以获得焦点
              tableContainer.setAttribute('tabindex', '0');
            }
            
            // 添加右键菜单支持
            table.on('cell-contextmenu', (args) => {
              const { col, row, event } = args;
              event.preventDefault();
              
              // 创建自定义右键菜单
              const menuDiv = document.createElement('div');
              menuDiv.className = 'vtable-context-menu';
              menuDiv.style.position = 'absolute';
              menuDiv.style.left = `${event.clientX}px`;
              menuDiv.style.top = `${event.clientY}px`;
              menuDiv.style.backgroundColor = '#fff';
              menuDiv.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
              menuDiv.style.borderRadius = '4px';
              menuDiv.style.padding = '5px 0';
              menuDiv.style.zIndex = '1000';
              
              // 复制单元格内容
              const copyItem = document.createElement('div');
              copyItem.innerText = '复制单元格内容';
              copyItem.style.padding = '8px 12px';
              copyItem.style.cursor = 'pointer';
              copyItem.style.hover = 'backgroundColor: #f5f5f5';
              copyItem.addEventListener('mouseover', () => {
                copyItem.style.backgroundColor = '#f5f5f5';
              });
              copyItem.addEventListener('mouseout', () => {
                copyItem.style.backgroundColor = '#fff';
              });
              copyItem.addEventListener('click', () => {
                const cellValue = table.getCellValue(col, row);
                navigator.clipboard.writeText(cellValue).then(() => {
                  message.success('已复制到剪贴板');
                  document.body.removeChild(menuDiv);
                }).catch(err => {
                  message.error('复制失败: ' + err);
                  document.body.removeChild(menuDiv);
                });
              });
              menuDiv.appendChild(copyItem);
              
              // 复制整行
              const copyRowItem = document.createElement('div');
              copyRowItem.innerText = '复制整行数据';
              copyRowItem.style.padding = '8px 12px';
              copyRowItem.style.cursor = 'pointer';
              copyRowItem.addEventListener('mouseover', () => {
                copyRowItem.style.backgroundColor = '#f5f5f5';
              });
              copyRowItem.addEventListener('mouseout', () => {
                copyRowItem.style.backgroundColor = '#fff';
              });
              copyRowItem.addEventListener('click', () => {
                const rowData = table.getRowData(row);
                const rowText = Object.values(rowData).join('\t');
                navigator.clipboard.writeText(rowText).then(() => {
                  message.success('已复制整行数据到剪贴板');
                  document.body.removeChild(menuDiv);
                }).catch(err => {
                  message.error('复制失败: ' + err);
                  document.body.removeChild(menuDiv);
                });
              });
              menuDiv.appendChild(copyRowItem);
              
              document.body.appendChild(menuDiv);
              
              // 点击其他地方关闭菜单
              const closeMenu = () => {
                if (document.body.contains(menuDiv)) {
                  document.body.removeChild(menuDiv);
                }
                document.removeEventListener('click', closeMenu);
              };
              setTimeout(() => {
                document.addEventListener('click', closeMenu);
              }, 0);
            });
            
            setTableInstance(table);

            // 返回清理函数
            return () => {
              if (tableContainer) {
                tableContainer.removeEventListener('keydown', handleKeyDown);
              }
            };

          } catch (error) {
            console.error('VTable初始化失败:', error);
          }
        }
      }, 200);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [extractedData]);

  // 页面加载时获取分析参数或测试结果ID
  useEffect(() => {
    // 检查URL参数中是否有result_id
    const urlParams = new URLSearchParams(window.location.search);
    const resultId = urlParams.get('result_id');

    if (resultId) {
      // 如果有result_id，加载测试结果数据
      loadTestResultData(resultId);
    } else {
      // 否则从sessionStorage获取分析参数
      const params = sessionStorage.getItem('logAnalysisParams');

      if (params) {
        try {
          const parsedParams = JSON.parse(params);
          setAnalysisParams(parsedParams);
        } catch (error) {
          message.error('参数解析失败，请重新选择文件');
          history.goBack();
        }
      } else {
        message.error('缺少分析参数，请重新选择文件');
        history.goBack();
      }
    }
  }, [history]);

  // 自动开始分析
  useEffect(() => {
    if (analysisParams && !loading && extractedData.length === 0) {
      processFiles();
    }
  }, [analysisParams, loading, extractedData.length, processFiles]);

  return (
    <div style={{ padding: '24px' }}>
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <a onClick={() => history.goBack()}>智能结果收集器</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>VTable数据分析</Breadcrumb.Item>
      </Breadcrumb>

      <Card
        title={
          <Space>
            <BarChartOutlined />
            <span>基准测试数据分析</span>
            {analysisParams && (
              <Tag color="blue">
                {analysisParams.selectedFiles?.length || 0} 个文件
              </Tag>
            )}
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />}
              onClick={() => history.goBack()}
            >
              返回
            </Button>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={processFiles}
              loading={loading}
              disabled={!analysisParams}
            >
              重新分析
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSave}
              disabled={extractedData.length === 0}
            >
              保存结果
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportData}
              disabled={extractedData.length === 0}
            >
              导出Excel
            </Button>
            <Button
              onClick={() => {
                if (tableInstance) {
                  try {
                    // 先选择所有内容
                    tableInstance.selectAll();
                    // 延迟一下确保选择完成
                    setTimeout(() => {
                      // 复制选中的内容
                      tableInstance.copy();
                      message.success('已复制表格数据到剪贴板');
                    }, 100);
                  } catch (error) {
                    console.error('复制失败:', error);
                    // 备用方案：手动构建复制内容
                    const headers = VTABLE_COLUMNS.map(col => col.title).join('\t');
                    const rows = extractedData.map(row =>
                      VTABLE_COLUMNS.map(col => row[col.field] || '').join('\t')
                    ).join('\n');
                    const content = headers + '\n' + rows;

                    navigator.clipboard.writeText(content).then(() => {
                      message.success('已复制表格数据到剪贴板');
                    }).catch(err => {
                      message.error('复制失败: ' + err.message);
                    });
                  }
                }
              }}
              disabled={extractedData.length === 0}
            >
              复制表格
            </Button>
          </Space>
        }
      >
        {loading && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Progress percent={progress} status="active" />
              <p>正在分析日志文件... ({progress}%)</p>
            </div>
          </div>
        )}
        
        {!loading && extractedData.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <BarChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <p>等待分析数据...</p>
          </div>
        )}

        {!loading && extractedData.length > 0 && (
          <div>
            <div style={{
              marginBottom: 8,
              padding: '8px 12px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#52c41a'
            }}>
              💡 提示：点击表格单元格可以选择，使用 Ctrl+C 快捷键或点击"复制表格"按钮复制数据，右键单击可复制单个单元格或整行数据
            </div>
            <div
              ref={containerRef}
              style={{
                height: '70vh',
                width: '100%',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                overflow: 'hidden',
                backgroundColor: '#fff',
                cursor: 'default'
              }}
            />
          </div>
        )}
      </Card>

      {/* 保存结果模态框 */}
      <Modal
        title="保存分析结果"
        visible={saveModalVisible}
        onOk={confirmSave}
        onCancel={() => setSaveModalVisible(false)}
        confirmLoading={saving}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            结果名称 <span style={{ color: 'red' }}>*</span>
          </label>
          <Input
            value={saveName}
            onChange={(e) => setSaveName(e.target.value)}
            placeholder="请输入结果名称"
            maxLength={100}
          />
        </div>
        <div>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            结果描述
          </label>
          <Input.TextArea
            value={saveDescription}
            onChange={(e) => setSaveDescription(e.target.value)}
            placeholder="请输入结果描述（可选）"
            rows={4}
            maxLength={500}
          />
        </div>
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 4 }}>
          <div style={{ fontSize: 12, color: '#666' }}>
            <div>数据统计：{extractedData.length} 个文件</div>
            <div>分析时间：{new Date().toLocaleString('zh-CN')}</div>
            {analysisParams && (
              <>
                <div>主机ID：{analysisParams.hostId}</div>
                <div>容器名称：{analysisParams.containerName}</div>
              </>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default VTableAnalysisPage;
