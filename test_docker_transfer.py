#!/usr/bin/env python3
"""
Docker文件分发功能测试脚本
用于测试新增的Docker容器分发功能
"""

import requests
import json
import sys
import os

# 配置
BASE_URL = "http://localhost:8000"  # 根据实际情况修改
API_TOKEN = ""  # 需要设置有效的API token

def test_docker_containers_api():
    """测试Docker容器列表API"""
    print("测试Docker容器列表API...")
    
    url = f"{BASE_URL}/api/exec/docker-containers/"
    headers = {"X-Token": API_TOKEN} if API_TOKEN else {}
    
    # 测试参数
    params = {"host_id": 1}  # 需要替换为实际的主机ID
    
    try:
        response = requests.get(url, params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_docker_path_suggestions_api():
    """测试Docker路径建议API"""
    print("\n测试Docker路径建议API...")
    
    url = f"{BASE_URL}/api/exec/docker-path-suggestions/"
    headers = {"X-Token": API_TOKEN} if API_TOKEN else {}
    
    # 测试参数
    params = {
        "host_id": 1,  # 需要替换为实际的主机ID
        "container_name": "test_container",  # 需要替换为实际的容器名
        "path_prefix": "/usr/"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_transfer_templates_api():
    """测试配置模板API"""
    print("\n测试配置模板API...")
    
    url = f"{BASE_URL}/api/exec/transfer-templates/"
    headers = {"X-Token": API_TOKEN} if API_TOKEN else {}
    
    # 测试获取模板列表
    try:
        response = requests.get(url, headers=headers)
        print(f"获取模板列表 - 状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        # 测试创建模板
        template_data = {
            "name": "测试模板",
            "description": "这是一个测试模板",
            "config": {
                "dst_dir": "/tmp/test",
                "use_docker": True,
                "docker_containers": {"1": "test_container"},
                "host_ids": [1],
                "host_names": [{"id": 1, "name": "测试主机", "hostname": "test.example.com"}]
            },
            "is_public": False
        }
        
        response = requests.post(url, json=template_data, headers=headers)
        print(f"创建模板 - 状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        return response.status_code in [200, 201]
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def test_docker_transfer():
    """测试Docker文件分发"""
    print("\n测试Docker文件分发...")
    
    url = f"{BASE_URL}/api/exec/transfer/"
    headers = {"X-Token": API_TOKEN} if API_TOKEN else {}
    
    # 创建测试文件
    test_file_content = "这是一个测试文件内容"
    
    # 准备表单数据
    files = {'file0': ('test.txt', test_file_content, 'text/plain')}
    data = {
        'data': json.dumps({
            'dst_dir': '/tmp/docker_test',
            'host_ids': [1],  # 需要替换为实际的主机ID
            'use_docker': True,
            'docker_containers': {'1': 'test_container'}  # 需要替换为实际的容器名
        })
    }
    
    try:
        response = requests.post(url, files=files, data=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Docker文件分发功能...")
    
    if not API_TOKEN:
        print("警告: 未设置API_TOKEN，某些测试可能失败")
    
    tests = [
        ("Docker容器列表API", test_docker_containers_api),
        ("Docker路径建议API", test_docker_path_suggestions_api),
        ("配置模板API", test_transfer_templates_api),
        ("Docker文件分发", test_docker_transfer),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置和环境")
        return 1

if __name__ == "__main__":
    sys.exit(main())
