#!/usr/bin/env python3
"""
测试Docker容器API
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_TOKEN = ""  # 如果需要的话

def test_docker_containers_api():
    """测试Docker容器列表API"""
    print("测试Docker容器列表API...")
    
    url = f"{BASE_URL}/api/exec/docker-containers/"
    headers = {"X-Token": API_TOKEN} if API_TOKEN else {}
    
    # 测试参数 - 需要替换为实际的主机ID
    params = {"host_id": 1}  # 请替换为实际的主机ID
    
    try:
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        response = requests.get(url, params=params, headers=headers)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success'):
                    containers = data.get('containers', [])
                    print(f"✅ 成功获取到 {len(containers)} 个容器")
                    for container in containers:
                        print(f"  - {container.get('name')} ({container.get('image')})")
                else:
                    print(f"❌ API返回失败: {data.get('error', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")

def test_with_different_host_ids():
    """测试不同的主机ID"""
    print("\n测试不同的主机ID...")
    
    # 测试几个可能的主机ID
    for host_id in [1, 2, 3]:
        print(f"\n--- 测试主机ID: {host_id} ---")
        
        url = f"{BASE_URL}/api/exec/docker-containers/"
        params = {"host_id": host_id}
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    containers = data.get('containers', [])
                    print(f"✅ 主机 {host_id}: 找到 {len(containers)} 个容器")
                else:
                    print(f"⚠️  主机 {host_id}: {data.get('error', '未知错误')}")
            else:
                print(f"❌ 主机 {host_id}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 主机 {host_id}: 请求异常 - {e}")

if __name__ == "__main__":
    print("开始测试Docker容器API...")
    test_docker_containers_api()
    test_with_different_host_ids()
    print("\n测试完成！")
