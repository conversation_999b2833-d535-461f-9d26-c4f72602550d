#!/usr/bin/env python3
"""
服务器指标优化脚本
- 移除 ServerMetrics 模型和数据库表
- 优化为实时获取，不再持久化存储
"""

import os
import sys
import django
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / 'spug_api'
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from django.core.management import execute_from_command_line
from django.db import connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("=" * 60)
    print("服务器指标优化脚本")
    print("=" * 60)
    
    try:
        # 1. 检查当前表状态
        print("\n1. 检查当前 model_storage_server_metrics 表状态...")
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) as row_count, 
                       ROUND(SUM(LENGTH(disk_usage) + LENGTH(cpu_usage) + LENGTH(memory_usage) + 
                                LENGTH(network_upload) + LENGTH(network_download) + LENGTH(network_total)) / 1024.0, 2) as size_kb
                FROM model_storage_server_metrics
            """)
            result = cursor.fetchone()
            if result:
                row_count, size_kb = result
                print(f"   - 当前记录数: {row_count}")
                print(f"   - 估算大小: {size_kb or 0} KB")
            else:
                print("   - 表不存在或无数据")
    
    except Exception as e:
        print(f"   - 检查表状态失败: {e}")
    
    # 2. 确认操作
    print("\n2. 优化说明:")
    print("   - 移除 ServerMetrics 模型和数据库表")
    print("   - 服务器指标改为实时获取，不再存储到数据库")
    print("   - 减少数据库存储压力，提高响应速度")
    print("   - 避免 SQLite 锁定问题")
    
    confirm = input("\n是否继续执行优化? (输入 'yes' 继续): ")
    if confirm.lower() != 'yes':
        print("操作已取消")
        return
    
    # 3. 应用迁移
    print("\n3. 应用数据库迁移...")
    try:
        os.chdir('spug_api')
        execute_from_command_line(['manage.py', 'migrate', 'model_storage'])
        print("   ✓ 迁移应用成功")
    except Exception as e:
        print(f"   ✗ 迁移应用失败: {e}")
        return
    
    # 4. 验证表是否已删除
    print("\n4. 验证表删除状态...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='model_storage_server_metrics'
            """)
            result = cursor.fetchone()
            if result:
                print("   ✗ 表仍然存在")
            else:
                print("   ✓ 表已成功删除")
    except Exception as e:
        print(f"   - 验证失败: {e}")
    
    # 5. 显示优化结果
    print("\n5. 优化完成!")
    print("   ✓ ServerMetrics 模型已移除")
    print("   ✓ model_storage_server_metrics 表已删除")
    print("   ✓ 服务器指标现在为实时获取")
    print("   ✓ 减少了数据库存储压力")
    
    print("\n6. 后续步骤:")
    print("   - 重启 Django 应用服务")
    print("   - 测试服务器指标功能是否正常")
    print("   - 监控系统性能改善情况")
    
    print("\n" + "=" * 60)
    print("优化完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
